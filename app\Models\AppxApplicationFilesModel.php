<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * AppxApplicationFilesModel
 *
 * Model for the appx_application_files table
 */
class AppxApplicationFilesModel extends Model
{
    protected $table         = 'appx_application_files';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'application_id',
        'applicant_id',
        'applicant_file_id',
        'file_title',
        'file_description',
        'file_path',
        'extracted_texts',
        'ai_analysis_results',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'application_id' => 'required|numeric',
        'applicant_id'   => 'required|numeric',
        'file_title'     => 'required|max_length[255]',
        'file_path'      => 'required|max_length[255]'
    ];

    protected $validationMessages = [
        'application_id' => [
            'required' => 'Application ID is required',
            'numeric'  => 'Application ID must be a number'
        ],
        'applicant_id' => [
            'required' => 'Applicant ID is required',
            'numeric'  => 'Applicant ID must be a number'
        ],
        'file_title' => [
            'required'    => 'File title is required',
            'max_length'  => 'File title cannot exceed 255 characters'
        ],
        'file_path' => [
            'required'    => 'File path is required',
            'max_length'  => 'File path cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get files by application ID
     *
     * @param int $applicationId
     * @return array
     */
    public function getFilesByApplicationId($applicationId)
    {
        return $this->where('application_id', $applicationId)->findAll();
    }

    /**
     * Get files by applicant ID
     *
     * @param int $applicantId
     * @return array
     */
    public function getFilesByApplicantId($applicantId)
    {
        return $this->where('applicant_id', $applicantId)->findAll();
    }

    /**
     * Get file by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getFileById($id)
    {
        return $this->find($id);
    }

    /**
     * Get files with extracted text
     *
     * @param int $applicationId
     * @return array
     */
    public function getFilesWithExtractedText($applicationId)
    {
        return $this->where('application_id', $applicationId)
                    ->where('extracted_texts IS NOT NULL')
                    ->where('extracted_texts !=', '')
                    ->findAll();
    }

    /**
     * Search files by title
     *
     * @param string $search
     * @param int $applicationId
     * @return array
     */
    public function searchFilesByTitle($search, $applicationId = null)
    {
        $builder = $this->like('file_title', $search);
        
        if ($applicationId !== null) {
            $builder->where('application_id', $applicationId);
        }
        
        return $builder->findAll();
    }

    /**
     * Get file count by application
     *
     * @param int $applicationId
     * @return int
     */
    public function getFileCountByApplication($applicationId)
    {
        return $this->where('application_id', $applicationId)->countAllResults();
    }

    /**
     * Delete files by application ID
     *
     * @param int $applicationId
     * @param int $deletedBy
     * @return bool
     */
    public function deleteFilesByApplication($applicationId, $deletedBy = null)
    {
        $data = [];
        if ($deletedBy !== null) {
            $data['deleted_by'] = $deletedBy;
        }
        
        return $this->where('application_id', $applicationId)->delete(null, true);
    }
}
