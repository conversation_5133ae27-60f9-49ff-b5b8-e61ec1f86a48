<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="<?= base_url('applicant/application/' . $application['id']) ?>" class="btn btn-outline-primary mb-3">
                        <i class="fas fa-arrow-left me-2"></i>Back to Application
                    </a>
                    <h1 class="h3 mb-2">
                        Edit Application Files
                        <span class="badge bg-primary ms-2"><?= esc($application['application_number']) ?></span>
                    </h1>
                    <p class="text-muted">
                        <strong>Position:</strong> <?= esc($position['designation']) ?><br>
                        <strong>Organization:</strong> <?= esc($organization['org_name']) ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Upload New File Section -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>Upload New File
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open_multipart(base_url('applicant/application/' . $application['id'] . '/files/upload'), ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="mb-3">
                            <label for="file_title" class="form-label">File Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="file_title" name="file_title" 
                                   value="<?= old('file_title') ?>" required maxlength="255">
                            <div class="invalid-feedback">
                                Please provide a file title.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="file_description" class="form-label">File Description</label>
                            <textarea class="form-control" id="file_description" name="file_description" 
                                      rows="3" maxlength="500"><?= old('file_description') ?></textarea>
                            <div class="form-text">Optional description of the file content.</div>
                        </div>

                        <div class="mb-3">
                            <label for="file" class="form-label">Select File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="file" name="file"
                                   accept=".pdf" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Only PDF files are allowed. Maximum file size: 25MB.
                            </div>
                            <div class="invalid-feedback">
                                Please select a valid PDF file.
                            </div>
                        </div>

                        <!-- File Processing Status -->
                        <div id="file-processing-status" class="mb-3" style="display: none;">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info mb-3">
                                        <i class="fas fa-cogs me-2"></i>Processing File
                                    </h6>

                                    <!-- Processing Steps -->
                                    <div id="processing-steps" class="mb-3">
                                        <div class="processing-step pending" id="step-validation">
                                            <div class="step-icon">1</div>
                                            <span>File validation</span>
                                        </div>
                                        <div class="processing-step pending" id="step-conversion">
                                            <div class="step-icon">2</div>
                                            <span>PDF to image conversion</span>
                                        </div>
                                        <div class="processing-step pending" id="step-ai-ready">
                                            <div class="step-icon">3</div>
                                            <span>Ready for AI analysis</span>
                                        </div>
                                    </div>

                                    <div id="conversion-status" class="mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <span>Converting PDF pages to images...</span>
                                        </div>
                                        <div class="progress mt-2" style="height: 8px;">
                                            <div id="conversion-progress" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                        </div>
                                        <small id="conversion-details" class="text-muted"></small>
                                    </div>

                                    <div id="ai-analysis-section" style="display: none;">
                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                            <span class="text-success">
                                                <i class="fas fa-check-circle me-1"></i>
                                                PDF conversion completed successfully!
                                            </span>
                                            <small class="text-muted" id="file-info"></small>
                                        </div>
                                        <button type="button" id="analyze-with-ai" class="btn btn-success">
                                            <i class="fas fa-brain me-2"></i>Process Document with AI
                                        </button>
                                        <div id="ai-processing" class="mt-3" style="display: none;">
                                            <div class="d-flex align-items-center">
                                                <div class="spinner-border spinner-border-sm text-success me-2" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <span>Analyzing document with AI...</span>
                                            </div>
                                            <div class="progress mt-2" style="height: 8px;">
                                                <div id="ai-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            <small id="ai-details" class="text-muted"></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary" id="upload-btn">
                            <i class="fas fa-upload me-2"></i>Upload File
                        </button>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Current Files Section -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>Current Files (<?= count($files) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($files)): ?>
                        <div class="list-group">
                            <?php foreach ($files as $file): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                                <?= esc($file['file_title']) ?>
                                            </h6>
                                            <?php if (!empty($file['file_description'])): ?>
                                                <p class="mb-1 text-muted small"><?= esc($file['file_description']) ?></p>
                                            <?php endif; ?>
                                            <small class="text-muted">
                                                Uploaded: <?= date('M d, Y', strtotime($file['created_at'])) ?>
                                            </small>
                                        </div>
                                        <div class="btn-group-vertical ms-3">
                                            <?php if (!empty($file['file_path'])): ?>
                                                <?php
                                                // Check if file exists using correct path
                                                $physicalPath = ROOTPATH . $file['file_path'];
                                                $fileExists = file_exists($physicalPath);
                                                ?>
                                                <?php if ($fileExists): ?>
                                                    <a href="<?= base_url($file['file_path']) ?>"
                                                       target="_blank"
                                                       class="btn btn-sm btn-outline-primary mb-1">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </a>
                                                <?php else: ?>
                                                    <span class="btn btn-sm btn-outline-secondary mb-1 disabled">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>File Missing
                                                    </span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="btn btn-sm btn-outline-secondary mb-1 disabled">
                                                    <i class="fas fa-question me-1"></i>No File Path
                                                </span>
                                            <?php endif; ?>
                                            
                                            <?= form_open(base_url('applicant/application/' . $application['id'] . '/files/delete/' . $file['id']), 
                                                ['class' => 'delete-form', 'style' => 'display: inline;']) ?>
                                                <button type="submit" class="btn btn-sm btn-outline-danger delete-btn">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </button>
                                            <?= form_close() ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No files uploaded yet.</p>
                            <p class="text-muted small">Use the upload form to add your first file.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>File Management Guidelines
                    </h6>
                    <ul class="mb-0 small">
                        <li>You can only edit files for applications with <strong>published</strong> exercise status.</li>
                        <li>Only PDF files are accepted with a maximum size of 25MB.</li>
                        <li>You can delete existing files and upload new ones, but cannot modify existing files.</li>
                        <li>Provide clear, descriptive titles for your files to help with identification.</li>
                        <li>All changes are immediately saved and cannot be undone.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item {
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-group-vertical .btn {
    border-radius: 0.25rem;
}

.btn-group-vertical .btn + .btn {
    margin-top: 0.25rem;
}

/* File processing styles */
#file-processing-status .card {
    border-left: 4px solid #17a2b8;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* AI Analysis Results */
.analysis-results {
    max-height: 400px;
    overflow-y: auto;
}

.analysis-results pre {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Processing status indicators */
.processing-step {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.processing-step .step-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 0.8rem;
}

.processing-step.completed .step-icon {
    background-color: #28a745;
    color: white;
}

.processing-step.active .step-icon {
    background-color: #17a2b8;
    color: white;
}

.processing-step.pending .step-icon {
    background-color: #6c757d;
    color: white;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- PDF.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script>
// Gemini AI Configuration
const GEMINI_API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
const GEMINI_MODEL = 'gemini-2.5-flash';
const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

// Global variables for file processing
let currentFile = null;
let convertedImages = [];
let totalPages = 0;
let aiAnalysisResult = null;

$(document).ready(function() {
    // Form validation and submission
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        } else {
            // Add AI analysis results to form if available
            if (aiAnalysisResult && aiAnalysisResult.length > 0) {
                // Create hidden input for AI analysis results
                const aiDataInput = $('<input>').attr({
                    type: 'hidden',
                    name: 'ai_analysis_results',
                    value: JSON.stringify(aiAnalysisResult)
                });
                $(this).append(aiDataInput);
            }
        }
        $(this).addClass('was-validated');
    });

    // Delete confirmation
    $('.delete-btn').on('click', function(e) {
        e.preventDefault();
        const form = $(this).closest('.delete-form');

        if (confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
            form.submit();
        }
    });

    // File input change handler
    $('#file').on('change', function() {
        const file = this.files[0];
        if (file) {
            // Check file size (25MB = 25 * 1024 * 1024 bytes)
            if (file.size > 25 * 1024 * 1024) {
                alert('File size must be less than 25MB.');
                this.value = '';
                return;
            }

            // Check file type
            if (file.type !== 'application/pdf') {
                alert('Only PDF files are allowed.');
                this.value = '';
                return;
            }

            // Start processing the file
            currentFile = file;
            startFileProcessing(file);
        } else {
            // Reset if no file selected
            resetProcessingStatus();
        }
    });

    // AI Analysis button handler
    $('#analyze-with-ai').on('click', function() {
        if (convertedImages.length > 0) {
            analyzeWithAI();
        } else {
            alert('Please wait for PDF conversion to complete first.');
        }
    });
});

// Start file processing workflow
function startFileProcessing(file) {
    $('#file-processing-status').show();
    $('#upload-btn').prop('disabled', true);

    // Reset previous state
    convertedImages = [];
    aiAnalysisResult = null;

    // Start PDF to image conversion
    convertPDFToImages(file);
}

// Reset processing status
function resetProcessingStatus() {
    $('#file-processing-status').hide();
    $('#upload-btn').prop('disabled', false);
    convertedImages = [];
    aiAnalysisResult = null;
    currentFile = null;
}

// Convert PDF to images
async function convertPDFToImages(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
        totalPages = pdf.numPages;

        $('#conversion-details').text(`Processing ${totalPages} pages...`);

        convertedImages = [];

        for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
            const page = await pdf.getPage(pageNum);
            const viewport = page.getViewport({ scale: 1.5 });

            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            await page.render({
                canvasContext: context,
                viewport: viewport
            }).promise;

            // Convert canvas to base64 image
            const imageData = canvas.toDataURL('image/jpeg', 0.8);
            convertedImages.push({
                pageNumber: pageNum,
                imageData: imageData
            });

            // Update progress
            const progress = (pageNum / totalPages) * 100;
            $('#conversion-progress').css('width', progress + '%');
            $('#conversion-details').text(`Converted page ${pageNum} of ${totalPages}`);
        }

        // Conversion complete
        $('#conversion-status').html(`
            <div class="d-flex align-items-center text-success">
                <i class="fas fa-check-circle me-2"></i>
                <span>PDF conversion completed successfully!</span>
            </div>
        `);

        $('#conversion-details').text(`${totalPages} pages converted to images`);
        $('#ai-analysis-section').show();
        $('#upload-btn').prop('disabled', false);

    } catch (error) {
        console.error('PDF conversion error:', error);
        $('#conversion-status').html(`
            <div class="d-flex align-items-center text-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>Error converting PDF to images</span>
            </div>
        `);
        $('#conversion-details').text('Error: ' + error.message);
        $('#upload-btn').prop('disabled', false);
    }
}

// Analyze document with AI
async function analyzeWithAI() {
    if (convertedImages.length === 0) {
        alert('No images available for analysis');
        return;
    }

    $('#ai-processing').show();
    $('#analyze-with-ai').prop('disabled', true);

    try {
        const fileName = currentFile.name;
        const totalChunks = Math.ceil(totalPages / (totalPages > 20 ? 10 : 5));
        const chunkSize = totalPages > 20 ? 10 : 5;

        let allAnalysisResults = [];

        for (let chunkNumber = 1; chunkNumber <= totalChunks; chunkNumber++) {
            const startPage = (chunkNumber - 1) * chunkSize + 1;
            const endPage = Math.min(chunkNumber * chunkSize, totalPages);

            $('#ai-details').text(`Analyzing pages ${startPage}-${endPage} (chunk ${chunkNumber}/${totalChunks})`);

            // Get images for this chunk
            const chunkImages = convertedImages.slice(startPage - 1, endPage);

            // Create the analysis prompt
            const chunkInfo = totalChunks > 1 ? ` (Chunk ${chunkNumber}/${totalChunks}, Pages ${startPage}-${endPage})` : '';

            const prompt = `
You are an expert document analyst. Analyze each page of this document and create a page-by-page profile focusing on document type, content summary, and notable features.

DOCUMENT: ${fileName}${chunkInfo}

ANALYSIS APPROACH:
For each page, provide:
1. PAGE TYPE: What type of document/page is this (e.g., CV page 1, certificate, transcript page 2, cover letter, etc.)
2. CONTENT SUMMARY: Brief summary of what information this page contains
3. NOTABLE FEATURES: Visual elements like stamps, signatures, handwritten notes, headers, logos, photos, etc.
4. PAGE RELATIONSHIP: How this page relates to the document (first page, continuation, last page, standalone, etc.)
5. AUTHENTICITY CHECK: Does this page appear to belong to the applicant? Any red flags?

Create a simple JSON structure like this:

{
    "document_profile": {
        "file_name": "${fileName}",
        "analysis_date": "${new Date().toISOString()}",
        "total_pages_analyzed": ${totalChunks > 1 ? `${endPage - startPage + 1}` : totalPages}${totalChunks > 1 ? `,
        "chunk_info": {
            "chunk_number": ${chunkNumber},
            "total_chunks": ${totalChunks},
            "pages_range": "${startPage}-${endPage}"
        }` : ''}
    },
    "page_analysis": [
        {
            "page_number": 1,
            "page_type": "What type of document/page is this (e.g., CV page 1, certificate, transcript page 2, cover letter, etc.)",
            "content_summary": "Brief summary of what information this page contains - don't extract everything, just summarize",
            "notable_features": [
                "List visual elements like: stamps, signatures, handwritten notes, headers, logos, photos, tables, etc."
            ],
            "page_relationship": "How this page relates to the document (first page, continuation, last page, standalone, part of multi-page document, etc.)",
            "authenticity_check": {
                "belongs_to_applicant": "YES/NO/UNCERTAIN",
                "concerns": ["Any red flags or suspicious elements"],
                "confidence": "HIGH/MEDIUM/LOW"
            }
        }
    ],
    "overall_document_assessment": {
        "document_type": "What type of document is this overall",
        "document_purpose": "What is this document's main purpose",
        "authenticity_verdict": "AUTHENTIC/QUESTIONABLE/REQUIRES_VERIFICATION",
        "key_observations": ["Most important observations about this document"],
        "recommended_actions": ["What should be done with this document"]
    }
}

ANALYSIS INSTRUCTIONS:

1. Look at each page individually
2. For each page, identify:
   - What type of page/document it is
   - Summarize the content (don't extract everything)
   - Note visual features (stamps, signatures, logos, etc.)
   - Determine how it relates to the overall document
   - Check if it appears to belong to the applicant

3. Focus on PROFILING the document, not extracting all data
4. Pay attention to authenticity - does this document belong to the applicant?
5. Note any suspicious elements or red flags

Example of what I want:
"Page 1 is the first page of a CV, contains personal information and contact details, has a professional header with the applicant's name, this page appears authentic and belongs to the applicant"

"Page 2 is a continuation of the CV showing work experience section, contains employment history from 2018-2023, has consistent formatting with page 1, this page is a continuation of the CV document"

Keep it simple and focused on document profiling, not data extraction.
`;

            // Prepare the request payload for Gemini AI
            const requestPayload = {
                contents: [{
                    parts: [
                        { text: prompt },
                        ...chunkImages.map(img => ({
                            inline_data: {
                                mime_type: "image/jpeg",
                                data: img.imageData.split(',')[1] // Remove data:image/jpeg;base64, prefix
                            }
                        }))
                    ]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 32,
                    topP: 1,
                    maxOutputTokens: 8192,
                }
            };

            // Make API call to Gemini
            const response = await fetch(GEMINI_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestPayload)
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            if (result.candidates && result.candidates[0] && result.candidates[0].content) {
                const analysisText = result.candidates[0].content.parts[0].text;
                allAnalysisResults.push({
                    chunk: chunkNumber,
                    pages: `${startPage}-${endPage}`,
                    analysis: analysisText
                });
            } else {
                throw new Error('Invalid response format from Gemini AI');
            }

            // Update progress
            const progress = (chunkNumber / totalChunks) * 100;
            $('#ai-progress').css('width', progress + '%');
        }

        // Analysis complete
        aiAnalysisResult = allAnalysisResults;

        $('#ai-processing').html(`
            <div class="d-flex align-items-center text-success">
                <i class="fas fa-check-circle me-2"></i>
                <span>AI analysis completed successfully!</span>
            </div>
        `);

        $('#ai-details').text(`Analyzed ${totalPages} pages in ${totalChunks} chunk(s)`);

        // Show analysis results summary
        showAnalysisResults(allAnalysisResults);

    } catch (error) {
        console.error('AI analysis error:', error);
        $('#ai-processing').html(`
            <div class="d-flex align-items-center text-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>Error during AI analysis</span>
            </div>
        `);
        $('#ai-details').text('Error: ' + error.message);
    } finally {
        $('#analyze-with-ai').prop('disabled', false);
    }
}

// Show analysis results
function showAnalysisResults(results) {
    let summaryHtml = '<div class="mt-3"><h6 class="text-success">AI Analysis Summary:</h6>';

    results.forEach((result, index) => {
        summaryHtml += `
            <div class="card mt-2">
                <div class="card-body p-2">
                    <h6 class="card-title mb-1">Chunk ${result.chunk} (Pages ${result.pages})</h6>
                    <div class="small">
                        <pre style="white-space: pre-wrap; font-size: 0.8em; max-height: 200px; overflow-y: auto;">${result.analysis}</pre>
                    </div>
                </div>
            </div>
        `;
    });

    summaryHtml += '</div>';
    $('#ai-analysis-section').append(summaryHtml);
}
</script>
<?= $this->endSection() ?>
